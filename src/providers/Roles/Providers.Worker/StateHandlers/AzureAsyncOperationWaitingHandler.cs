﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Worker.StateHandlers
{
    using System;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Microsoft.WindowsAzure.ResourceStack.Common.Algorithms;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBAsyncContract;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBCSContract;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Metadata;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Services;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.Deployments;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.ManagedIdentity;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Metrics;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Engines;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    public class AzureAsyncOperationWaitingHandler : BaseHandler
    {
        private readonly IApplianceDataProvider ApplianceDataProvider;

        private readonly IAccessConnectorDataProvider AccessConnectorDataProvider;

        private readonly ApplianceProvisioningJobMetadata Metadata;

        private readonly IApplicationPackageDataProvider MarketPlacePackageDataProvider;

        private readonly ILogger Logger;

        private readonly IFrontdoorEngine FrontdoorEngine;

        private readonly IApplianceEngine ApplianceEngine;

        private readonly IStorageAccountManager StorageAccountManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="AzureAsyncOperationWaitingHandler" /> class.
        /// </summary>
        /// <param name="applianceDataProvider"></param>
        /// <param name="marketPlacePackageDataProvider"></param>
        /// <param name="frontdoorEngine"></param>
        /// <param name="logger"></param>
        /// <param name="metadata"></param>
        public AzureAsyncOperationWaitingHandler(IApplianceDataProvider applianceDataProvider,
                                   IAccessConnectorDataProvider accessConnectorDataProvider,
                                   IApplicationPackageDataProvider marketPlacePackageDataProvider,
                                   IFrontdoorEngine frontdoorEngine,
                                   IApplianceEngine applianceEngine,
                                   ILogger logger,
                                   ApplianceProvisioningJobMetadata metadata)
        {
            this.ApplianceDataProvider = applianceDataProvider;
            this.AccessConnectorDataProvider = accessConnectorDataProvider;
            this.Metadata = metadata;
            this.MarketPlacePackageDataProvider = marketPlacePackageDataProvider;
            this.Logger = logger;
            this.FrontdoorEngine = frontdoorEngine;
            this.ApplianceEngine = applianceEngine;
            this.StorageAccountManager = new StorageAccountManager(frontdoorEngine,
                this.Logger.EventSource);
        }

        /// <summary>
        /// Handles workspace AzureAsyncOperationWaiting state
        /// </summary>
        /// <returns></returns>
        public async Task<JobExecutionResult> Execute()
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            ApplianceEntity existingEntity = await this
                            .ApplianceDataProvider
                            .FindAppliance(
                            subscriptionId: this.Metadata.SubscriptionId,
                            resourceGroupName: this.Metadata.ResourceGroupName,
                            applianceName: this.Metadata.ApplianceName)
                            .ConfigureAwait(continueOnCapturedContext: false);

            MarketplaceAppliancePackage appliancePackage = this.MarketPlacePackageDataProvider.FindMarketplaceAppliancePackage(existingEntity.Properties.PublisherPackageId);
            string publisherTenantId = appliancePackage.TenantId;
            string workspaceId = existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType);

            this.Logger.LogDebug(operationName, $"Handle azure async operation for the workspace '{workspaceId}'. OperationTrackingUri: {this.Metadata.OperationTrackingUri}");

            try
            {
                ApplianceEntity incomingEntity = new ApplianceEntity();
                this.Metadata.Parameters.TryGetValue(ProviderConstants.WorkspaceUpdateParameters.Properties, out JToken jTokenProperties);
                incomingEntity.Properties = JsonConvert.DeserializeObject<ApplicationProperties>(jTokenProperties.ToJson());

                this.Metadata.Parameters.TryGetValue(ProviderConstants.WorkspaceUpdateParameters.Sku, out JToken jTokenSku);
                incomingEntity.Sku = jTokenSku;

                this.Metadata.Parameters.TryGetValue(ProviderConstants.WorkspaceUpdateParameters.Tags, out JToken jTokenTags);
                incomingEntity.Tags = JsonConvert.DeserializeObject<InsensitiveDictionary<string>>(jTokenTags.ToJson());

                AsyncOperationResult asyncOperationResult = await this.FrontdoorEngine
                    .GetDeploymentOperationStatus(
                        publisherTenantId,
                        this.Metadata.OperationTrackingUri,
                        this.Metadata.ResourceProviderNamespace,
                        true)
                    .ConfigureAwait(false);

                if (asyncOperationResult.Status.EqualsInsensitively(ProvisioningState.Succeeded.ToString()))
                {
                    this.Logger.LogDebug(operationName, $"Azure async operation succeeded for the workspace '{workspaceId}'," +
                        $" appliance provisioning state: '{existingEntity.Properties.ProvisioningState}'");

                    ResourceGroupRequestMatch resourceGroupRequest = ResourceGroupRequestMatch.FromFullyQualifiedId(existingEntity.Properties.ManagedResourceGroupId.Trim());

                    DeploymentDefinition outputDeploymentDefinition = await this
                        .FrontdoorEngine
                        .GetDeployment(
                            publisherTenantId,
                            resourceGroupRequest.SubscriptionId,
                            resourceGroupRequest.ResourceGroup,
                            existingEntity.Metadata.DeploymentName,
                            this.Metadata.ResourceProviderNamespace)
                        .ConfigureAwait(false);

                    if (outputDeploymentDefinition != null
                        && outputDeploymentDefinition.Properties != null)
                    {
                        InsensitiveDictionary<JToken> parameters = outputDeploymentDefinition.Properties.Parameters;
                        Utilities.RemoveDeploymentParameters(parameters, this.Logger.EventSource);
                    }

                    existingEntity.Properties.Parameters = outputDeploymentDefinition.Properties.Parameters;
                    existingEntity.Properties.Authorizations = appliancePackage.Authorizations?.Select(auth => new ApplicationAuthorizationEntity()
                    {
                        PrincipalId = auth.PrincipalId,
                        RoleDefinitionId = auth.RoleDefinitionId
                    }).ToArray();

                    outputDeploymentDefinition.Properties.Outputs.TryParseDatabricksPropertiesParameter(ProviderConstants.Databricks.UcConnectorPrincipalId,
                            this.Logger.EventSource, out string ucConnectorPrincipalId);
                    outputDeploymentDefinition.Properties.Outputs.TryParseDatabricksPropertiesParameter(ProviderConstants.Databricks.UcConnectorTenantId,
                            this.Logger.EventSource, out string ucConnectorTenantId);

                    this.Logger.LogDebug(operationName, $"Captured ucaccessconnector principalId: {ucConnectorPrincipalId} and tenantId: {ucConnectorTenantId}");
                    var dbfsResourceId = Utilities.GetDbfsResourceId(existingEntity.Properties.Parameters, existingEntity.Properties.ManagedResourceGroupId);

                    var postponeInterval = TimeSpan.FromMinutes(10);

                    // These two values will not be empty during create workspace for Premium or Trail SKU
                    if (!string.IsNullOrWhiteSpace(ucConnectorPrincipalId) && !string.IsNullOrWhiteSpace(ucConnectorTenantId))
                    {
                        this.Logger.LogDebug(operationName, $"Excluding ucaccessconnector principalId: {ucConnectorPrincipalId} from deny assignment.");

                        await this.ApplianceEngine
                            .CreateOrUpdateDenyAssignmentsForApplication(
                                publisherTenantId,
                                RequestCorrelationContext.Current.GetHomeTenantId(),
                                existingEntity,
                                this.Metadata.ResourceType,
                                this.Metadata.ResourceProviderNamespace,
                                ucConnectorPrincipalId)
                            .ConfigureAwait(false);

                        this.Logger.LogDebug(operationName, $"Granting RBAC on DBFS for ucaccessconnector principalId: {ucConnectorPrincipalId}.");

                        await this.ApplianceEngine
                            .CreateRoleAssignmentIfNotExists(
                                ucConnectorPrincipalId,
                                PrincipalType.ServicePrincipal,
                                dbfsResourceId,
                                ProviderConstants.Storage.StorageBlobDataContributorRoleId,
                                existingEntity.SubscriptionId,
                                RequestCorrelationContext.Current.GetHomeTenantId(),
                                this.Metadata.ResourceProviderNamespace)
                        .ConfigureAwait(false);

                        var existingConnector = new AccessConnectorIdEntity
                        {
                            Id = $"{existingEntity.Properties.ManagedResourceGroupId}" +
                                    $"{ProviderConstants.Databricks.AccessConnectorIdPart}/" +
                                    $"{ProviderConstants.Databricks.UcConnectorName}",
                            PrincipalId = ucConnectorPrincipalId,
                            TenantId = ucConnectorTenantId,
                            IdentityType = IdentityType.SystemAssigned
                        };

                        #region defaultStorageFirewall Configuration

                        this.Logger.LogDebug(operationName,
                            $"Handle DefaultStorage Firewall Configuration with given defaultStoageFirewall: '{existingEntity.Properties.DefaultStorageFirewall}' " +
                            $"and modify uc connector '{existingConnector.Id}'");

                        var operationResult = await this.ApplianceEngine
                                .ConfigureDefaultStorageFirewall(
                                    existingEntity,
                                    existingConnector,
                                    null,
                                    publisherTenantId,
                                    dbfsResourceId)
                                .ConfigureAwait(false);

                        if (operationResult.Item1 != null)
                        {
                            this.Logger.LogDebug(operationName,
                                $"Encountered exception while handle defaultStoageFirewall configuration {operationResult.Item1.Message}.");

                            return operationResult.Item1;
                        }

                        existingEntity.Properties.AccessConnector = operationResult.Item2;
                        existingEntity.Properties.DefaultStorageFirewall = operationResult.Item3;

                        #endregion

                        this.Metadata.ResourceOperation = ProvisioningOperation.Waiting;
                        postponeInterval = this.PostponeInterval;
                    }
                    // This is the case for redeploy workspace operation
                    else if (existingEntity.Properties.ProvisioningState != ProvisioningState.Updating)
                    {
                        var existingAccessConnector = existingEntity.Metadata.ExistingAccessConnector;
                        var existingDefaultStorageFirewall = existingEntity.Metadata.ExistingDefaultStorageFirewall;

                        this.Logger.LogDebug(operationName,
                            $"Handle DefaultStorage Firewall Configuration with given defaultStoageFirewall: '{existingEntity.Properties.DefaultStorageFirewall}' " +
                            $"and modify uc connector '{existingAccessConnector?.Id}'" +
                            $"existing default storage firewall: '{existingDefaultStorageFirewall}'");

                        var operationResult = await this.ApplianceEngine
                                .ConfigureDefaultStorageFirewall(
                                    existingEntity,
                                    existingAccessConnector,
                                    existingDefaultStorageFirewall,
                                    publisherTenantId,
                                    dbfsResourceId)
                                .ConfigureAwait(false);

                        if (operationResult.Item1 != null)
                        {
                            this.Logger.LogDebug(operationName,
                                $"Encountered exception while handle defaultStoageFirewall configuration {operationResult.Item1.Message}.");

                            return operationResult.Item1;
                        }

                        existingEntity.Properties.AccessConnector = operationResult.Item2;
                        existingEntity.Properties.DefaultStorageFirewall = operationResult.Item3;

                        //Clean up managed resource group networking resources
                        if (existingEntity.Metadata.IsvNetInjectionMigrationInProgress)
                        {
                            await DeleteNetworkingResourcesInMrg(existingEntity.SubscriptionId, resourceGroupRequest.ResourceGroup, publisherTenantId).ConfigureAwait(false);
                        }

                        this.Metadata.ResourceOperation = ProvisioningOperation.Waiting;
                    }
                    // This is the case for workspace update with networking properties changes
                    else
                    {
                        var customVirtualNetworkId = existingEntity.Properties.Parameters.GetCanonicalizedCustomVirtualNetworkIdProperty(this.Logger.EventSource, string.Empty);
                        var customPublicSubnetName = existingEntity.Properties.Parameters.GetCustomPublicSubnetNameProperty(this.Logger.EventSource, string.Empty);
                        var customPrivateSubnetName = existingEntity.Properties.Parameters.GetCustomPrivateSubnetNameProperty(this.Logger.EventSource, string.Empty);
                        var enableNoPublicIp = existingEntity.Properties.Parameters.GetNoPublicIpOrDefault(this.Logger.EventSource);
                        var publicNetworkAccess = existingEntity.Properties.PublicNetworkAccess;
                        var requiredNsgRules = existingEntity.Properties.RequiredNsgRules;

                        this.Logger.LogDebug(
                            operationName,
                            $"Updated Network Properties before calling DB." +
                            $"Custom Virtual Network Id: '{customVirtualNetworkId}', " +
                            $"Custom Public Subnet Name: '{customPublicSubnetName}', " +
                            $"Custom Private Subnet Name: '{customPrivateSubnetName}'," +
                            $"Enable No Public IP : '{enableNoPublicIp}', " +
                            $"Public Network Access : '{publicNetworkAccess}', " +
                            $"RequiredNsgRules : '{requiredNsgRules}'.");

                        existingEntity.CopyFromIncomingEntityForDBNotification(incomingEntity, this.Logger.EventSource);

                        //// Notifies DB for Workspace UPDATE
                        var workspaceResourceId = existingEntity.GetFullyQualifiedResourceId();
                        var apiVersion = existingEntity.GetDatabricksBackendApiVersion(this.Logger.EventSource);

                        // Skip notifies DB for Workspace UPDATE for subscriptions registered with related feature;
                        // This should works only in staging environment
                        bool skipControlPlane = await this.ApplianceEngine.ShouldSkipControlPlane(existingEntity);
                        if (!skipControlPlane)
                        {
                            var notificationResult = await this.DBWorkspaceUpdateNotificationWithAccountApi(
                                workspaceResourceId,
                                existingEntity,
                                appliancePackage.TenantId,
                                apiVersion,
                                existingEntity.Properties.WorkspaceUrl)
                            .ConfigureAwait(false);

                            if (notificationResult != null)
                            {
                                // If we got a result from the notification, return it to start polling
                                return notificationResult;
                            }
                        }
                        else
                        {
                            this.Logger.LogDebug(operationName, $"Skip the step to Initialize DB control plane since the subscription registed to the feature '{ProviderConstants.Databricks.MicrosoftDatabricksSkipControlPlaneFeature}'.");
                        }

                        this.PopulatePropertiesFromAppliancePackage(existingEntity, appliancePackage);

                        this.Logger.LogDebug(operationName,
                            $"UPDATE on the Workspace '{workspaceResourceId}', Api-version: {RequestCorrelationContext.Current.ApiVersion} completed. " +
                            $"Workspace Properties :{existingEntity.Properties.ToJson()}");

                        await DeleteNetworkingResourcesInMrg(existingEntity.SubscriptionId, resourceGroupRequest.ResourceGroup, publisherTenantId).ConfigureAwait(false);
                        this.Metadata.ResourceOperation = ProvisioningOperation.PostDeploymentWaiting;
                    }

                    await this
                        .ApplianceDataProvider
                        .SaveAppliance(existingEntity)
                        .ConfigureAwait(false);

                    this.Logger.LogDebug(
                           operationName,
                           $"Waiting '{postponeInterval.TotalSeconds}' seconds for the post deployment operations for the '{workspaceId}'");

                    return new JobExecutionResult
                    {
                        Status = JobExecutionStatus.Postponed,
                        NextExecutionTime = DateTime.UtcNow.Add(postponeInterval),
                        Message = $"Successfully completed ARM deployment for the workspace '{workspaceId}'. Waiting for the post deployment operations. Postponing job",
                        Details = HttpStatusCode.OK.ToString(),
                        NextMetadata = this.Metadata.ToJson()
                    };
                }

                if (asyncOperationResult.Status.EqualsInsensitively(ProvisioningState.Failed.ToString())
                    || asyncOperationResult.Status.EqualsInsensitively(ProvisioningState.Canceled.ToString()))
                {
                    asyncOperationResult.Error = asyncOperationResult.Error ?? new ExtendedErrorInfo();

                    asyncOperationResult.Error.Code = ErrorResponseCode.ApplianceDeploymentFailed.ToString();
                    asyncOperationResult.Error.Message = ErrorResponseMessages.ApplianceDeploymentFailed.ToLocalizedMessage(existingEntity.Metadata.DeploymentName, existingEntity.Properties.ManagedResourceGroupId, asyncOperationResult.Error.Message);

                    this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                    this.Metadata.OperationResponseContent = AsyncOperationResult
                        .GetAsyncOperationResult(ProvisioningState.Failed, asyncOperationResult.Error)
                        .ToJToken();

                    await this
                       .ApplianceDataProvider
                       .SetApplianceProvisioningState(
                           subscriptionId: this.Metadata.SubscriptionId,
                           resourceGroupName: this.Metadata.ResourceGroupName,
                           applianceName: this.Metadata.ApplianceName,
                           provisioningState: ProvisioningState.Failed)
                       .ConfigureAwait(continueOnCapturedContext: false);

                    return new JobExecutionResult
                    {
                        Status = JobExecutionStatus.Completed,
                        Message = $"Failed to provision appliance resource '{workspaceId}'. Completing job",
                        Details = HttpStatusCode.Conflict.ToString(),
                        NextMetadata = this.Metadata.ToJson()
                    };
                }

                string message = $"Resource Type '{this.Metadata.ResourceType}' with id '{workspaceId}' has deployment has provisioning state '{asyncOperationResult.Status}'.";

                this.Logger.LogDebug(operationName, message);

                return this.PostponeJob(message, this.Metadata, this.PostponeInterval);
            }
            catch (DeploymentOperationException exception)
            {
                this.Logger.LogDebug(
                    operationName,
                    $"Operation to get deployment status of appliance '{workspaceId}' failed with exception '{Utilities.FlattenException(exception)}'.");

                if (this.IsDeploymentOperationExceptionRetryable(exception))
                {
                    this.Metadata.CurrentDeploymentOperationExceptionRetryableErrorCount++;
                    return this.PostponeJob(exception.Message, this.Metadata, this.PostponeInterval);
                }

                this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                this.Metadata.OperationResponseContent = AsyncOperationResult
                    .GetAsyncOperationResult(ProvisioningState.Failed, exception.ErrorCode, exception.Message)
                    .ToJToken();

                await this
                   .ApplianceDataProvider
                   .SetApplianceProvisioningState(
                       subscriptionId: this.Metadata.SubscriptionId,
                       resourceGroupName: this.Metadata.ResourceGroupName,
                       applianceName: this.Metadata.ApplianceName,
                       provisioningState: ProvisioningState.Failed)
                   .ConfigureAwait(continueOnCapturedContext: false);

                return new JobExecutionResult
                {
                    Status = JobExecutionStatus.Completed,
                    Message = "Failed to provision appliance resource. Completing job",
                    Details = HttpStatusCode.Conflict.ToString(),
                    NextMetadata = this.Metadata.ToJson()
                };
            }
            catch (Exception exception)
            {
                this.Logger.LogError(
                    operationName,
                    $"Operation failed appliance '{workspaceId}' failed with exception '{Utilities.FlattenException(exception)}'.");

                if (exception is ServerErrorResponseMessageException serverexception)
                {
                    this.Metadata.SLIErrorInfo = SLIErrorInfo.GetSLIErrorInfo(serverexception.HttpStatus, serverexception.ErrorCode);

                    if (serverexception.ErrorCode.Equals(ErrorResponseCode.PermissionDenied.ToString(),
                            StringComparison.InvariantCultureIgnoreCase))
                    {
                        // Set provisioning state back to succeeded. Since any of the above Update failure will not lead to workspace failed provisioning state.
                        // Update workflow only occurs if the workspace is in success state
                        await this.ApplianceDataProvider
                            .SetApplianceProvisioningState(existingEntity.SubscriptionId,
                                existingEntity.ResourceGroup,
                                existingEntity.Name,
                                ProvisioningState.Succeeded)
                            .ConfigureAwait(continueOnCapturedContext: false);

                        return this.HandleFailedJobExecutionResult(existingEntity.GetFullyQualifiedResourceId(),
                            this.Metadata,
                            serverexception.ErrorCode,
                            exception);
                    }
                }

                var failedJobExecutionResult = this.HandleFailedJobExecutionResult(existingEntity.GetFullyQualifiedResourceId(),
                    this.Metadata,
                    ErrorResponseCode.WorkspaceUpdateFailed.ToString(),
                    exception);

                if (failedJobExecutionResult.Status == JobExecutionStatus.Faulted && existingEntity?.Properties != null)
                {
                    await this.ApplianceDataProvider.SetApplianceProvisioningState(
                            subscriptionId: this.Metadata.SubscriptionId,
                            resourceGroupName: this.Metadata.ResourceGroupName,
                            applianceName: this.Metadata.ApplianceName,
                            provisioningState: ProvisioningState.Failed)
                        .ConfigureAwait(continueOnCapturedContext: false);
                }

                return failedJobExecutionResult;
            }
        }

        /// <summary>
        /// Deletes the networking resources in MRG.
        /// </summary>
        /// <param name="subscriptionId">subscription id of the managed resource group</param>
        /// <param name="resourceGroupName">name of the managed resource group</param>
        /// <param name="publisherTenantId">publisher tenant id of workspace</param>
        /// <returns></returns>
        private async Task DeleteNetworkingResourcesInMrg(string subscriptionId, string resourceGroupName, string publisherTenantId)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.Logger.LogDebug(operationName,
                $"Deleting Networking resources in Managed Resource Group as we are migrating to vNet injection");

            // Create filter query to networking resources created for managed vNet scenario
            string networkingResourcesQuery = $"resourceType eq '{ProviderConstants.MicrosoftNetwork.NsgResourceType}' or " +
                                    $"resourceType eq '{ProviderConstants.MicrosoftNetwork.PublicIpResourceType}' or " +
                                    $"resourceType eq '{ProviderConstants.MicrosoftNetwork.NatGatewayResourceType}' or " +
                                    $"resourceType eq '{ProviderConstants.MicrosoftNetwork.VirtualNetworkResourceType}' ";

            var networkingResources = await this.FrontdoorEngine.GetResourceGroupResources(
                    publisherTenantId,
                    subscriptionId,
                    resourceGroupName,
                    this.Metadata.ResourceProviderNamespace,
                    networkingResourcesQuery,
                    ProviderConstants.Databricks.MaxResourcesToQueryFromMrg);

            if (networkingResources.Any())
            {
                // Delete resources inside MRG except DBFS Account, Access Connector, User Assigned Identity
                // We are not waiting for the delete operation to complete as it is not required.
                await this.ApplianceEngine.DeleteResourcesInBulk(
                    authenticationTenantId: publisherTenantId,
                    networkingResources,
                    this.Metadata.ResourceProviderNamespace).ConfigureAwait(continueOnCapturedContext: false);
            }
        }

        /// <summary>
        /// Determines whether deployment operation exception can be retried.
        /// </summary>
        /// <param name="exception">The exception.</param>
        private bool IsDeploymentOperationExceptionRetryable(DeploymentOperationException exception)
        {
            // Note(wud):  we do this to account for ARM replication latency of RBAC data since PUT and GET deployments could hit different ARM regions.
            return (exception.HttpStatus.IsRetryableResponse() || exception.HttpStatus == HttpStatusCode.Forbidden || exception.HttpStatus == HttpStatusCode.Unauthorized) &&
                this.Metadata.CurrentDeploymentOperationExceptionRetryableErrorCount < this.MaxRetries;
        }

        /// <summary>
        /// Calls DB for the workspace UPDATE notification.
        /// </summary>
        /// <param name="workspaceId">The workspace Id.</param>
        /// <param name="notification">The notification object.</param>
        /// <param name="customerTenantId"> The customer tenant id</param>
        /// <param name="apiVersion">The API version.</param>
        /// <param name="workspaceUrl">The workspace URL</param>
        /// <returns>returns the DB response.</returns>
        private async Task<JobExecutionResult> DBWorkspaceUpdateNotificationWithAccountApi(
            string workspaceId,
            ApplianceEntity notification,
            string customerTenantId,
            string apiVersion,
            string workspaceUrl)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            (Uri baseUri, string audience, bool useArm) = await this.FrontdoorEngine
                 .GetDatabricksAccountApiEnpointAndAudience(customerTenantId, this.Metadata.SubscriptionId);

            // Skips the workspace PUT/ PATCH notification.
            if (!Utilities.IsWorkspaceNotificationEnabled(apiVersion))
            {
                this.Logger.LogDebug(
                    operationName,
                    $"SKIP: DB Workspace Notification (config/ AFEC) on WorkspaceId : '{workspaceId}'");

                return null;
            }

            // Skips the workspace PUT/ PATCH notification if WorkspaceUrl is null or empty.
            if (string.IsNullOrEmpty(workspaceUrl))
            {
                this.Logger.LogDebug(
                    operationName,
                    $"SKIP: DB Workspace Notification (empty workspace url) on WorkspaceId : '{workspaceId}'");

                return null;
            }

            try
            {
                // Skips the workspace PUT/ PATCH notification if linked AML Workspace Id doesn't exist.
                string amlWorkspaceId = null;

                if (useArm)
                {
                    amlWorkspaceId = notification.ToDbWorkspace()?.Update?.Properties?.Parameters?.AmlWorkspaceId?.Value<string>("value");
                }
                else
                {
                    amlWorkspaceId = DatabricksWorkspace.FromApplianceEntity(notification)?.Properties?.Parameters?.AmlWorkspaceId?.Value<string>("value");
                }

                this.Logger.LogDebug(
                        operationName,
                        $"DB Workspace Notification - Linked AML Workspace Id : '{amlWorkspaceId}'");

                if (!string.IsNullOrEmpty(amlWorkspaceId))
                {
                    JToken amlWorkspace = await this.FrontdoorEngine
                                            .GetAmlWorkspace(customerTenantId, amlWorkspaceId, ProviderConstants.Databricks.ResourceProviderNamespace)
                                            .ConfigureAwait(false);

                    if (amlWorkspace == default(JToken))
                    {
                        this.Logger.LogError(
                            operationName,
                            $"DB Workspace Notification - Linked AML Workspace Id : '{amlWorkspaceId}' does not exist.");

                        throw new ErrorResponseMessageException(
                            HttpStatusCode.Conflict,
                            ErrorResponseCode.LinkedAmlWorkspaceDoesNotExist,
                            ErrorResponseMessages.LinkedAmlWorkspaceDoesNotExist.ToLocalizedMessage(amlWorkspaceId));
                    }
                }
                var payload = useArm ?
                    JToken.FromObject(notification.ToDbWorkspace()) :
                    JToken.FromObject(DatabricksWorkspace.FromApplianceEntity(notification));
                this.Logger.LogDebug(
                operationName,
                    $"[Feature Flag selection]START: DB Workspace {(useArm ? "PATCH" : "PUT")} Notification on WorkspaceId: '{workspaceId}'" +
                    $"Using {(useArm ? "ARM" : "AccountApi")}");
                await DatabricksAccountsManagerUtils.SendDBNotificationWithFlagControl(
                    this.FrontdoorEngine,
                    payload,
                    useArm ? new HttpMethod("PATCH") : new HttpMethod("PUT"),
                    customerTenantId,
                    useArm,
                    workspaceId,
                    baseUri,
                    apiVersion,
                    audience).ConfigureAwait(false);
                if (!useArm)
                {
                    this.Metadata.ResourceOperation = ProvisioningOperation.AccountApiPolling;
                    this.Metadata.AccountApiPollingState = AccountApiPollingState.WaitingForUpdate;
                    this.Metadata.AccountApiOperationId = workspaceId;
                    this.Metadata.AccountApiOperationStartTime = DateTime.UtcNow;
                    this.Metadata.AccountApiBaseUri = baseUri;
                    this.Metadata.AccountApiAudience = audience;

                    // Set the provisioning state to Updating for job-based polling
                    notification.Properties.ProvisioningState = ProvisioningState.Updating;

                    await this.ApplianceDataProvider
                        .SaveAppliance(notification)
                        .ConfigureAwait(false);

                    return new JobExecutionResult
                    {
                        Status = JobExecutionStatus.Postponed,
                        NextExecutionTime = DateTime.UtcNow.Add(TimeSpan.FromSeconds(FrontdoorEngine.AccountApiPollingRetryInterval)),
                        Message = $"Waiting for Account API update operation to complete for workspace '{workspaceId}'",
                        NextMetadata = this.Metadata.ToJson()
                    };
                }

                return null;
            }
            catch (ErrorResponseMessageException errorResponseMessageException) when (errorResponseMessageException.ErrorCode == ErrorResponseCode.LinkedAmlWorkspaceDoesNotExist)
            {
                throw errorResponseMessageException;
            }
            catch (ServerErrorResponseMessageException)
            {
                throw;
            }
            catch (Exception)
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.Conflict,
                    ErrorResponseCode.WorkspacePatchFailed,
                    ErrorResponseMessages.WorkspacePatchFailed.ToLocalizedMessage(workspaceId));
            }
        }

        /// <summary>
        /// Populates appliance properties from an associated appliance package.
        /// </summary>
        /// <param name="applianceEntity">The appliance entity.</param>
        /// <param name="appliancePackage">The application package.</param>
        private void PopulatePropertiesFromAppliancePackage(ApplianceEntity applianceEntity, AppliancePackage appliancePackage)
        {
            applianceEntity.Properties.Authorizations = appliancePackage.Authorizations?.Select(auth => new ApplicationAuthorizationEntity()
            {
                PrincipalId = auth.PrincipalId,
                RoleDefinitionId = auth.RoleDefinitionId
            }).ToArray();
        }
    }
}
