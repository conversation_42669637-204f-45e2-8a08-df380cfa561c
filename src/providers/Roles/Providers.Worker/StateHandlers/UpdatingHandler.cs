﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Worker.StateHandlers
{
    using Microsoft.WindowsAzure.ResourceStack.Common.Algorithms;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBAsyncContract;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Metadata;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Services;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.Deployments;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Metrics;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Engines;
    using Newtonsoft.Json.Linq;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Threading.Tasks;

    public class UpdatingHandler : BaseHandler
    {
        private readonly IApplianceDataProvider ApplianceDataProvider;

        private readonly IAccessConnectorDataProvider AccessConnectorDataProvider;

        private readonly ApplianceProvisioningJobMetadata Metadata;

        private readonly IApplicationPackageDataProvider MarketPlacePackageDataProvider;

        private readonly ILogger Logger;

        private readonly IFrontdoorEngine FrontdoorEngine;

        private readonly IApplianceEngine ApplianceEngine;

        private readonly IWorkspaceTagService WorkspaceTagService;

        private readonly IVirtualNetworkManager VirtualNetworkManager;

        private readonly IStorageAccountManager StorageAccountManager;

        /// <summary>
        /// Gets a value indicating whether the environment is development.
        /// </summary>
        private bool IsDevEnvironment
        {
            get
            {
                return
                    CloudConfigurationManager.IsCloudEnvironmentEmulated &&
                    CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.RoleLocation", string.Empty)
                                                .StartsWithInsensitively("DevFabric");
            }
        }

        /// <summary>
        /// Gets a value to postpone the job after DB Notification.
        /// </summary>
        private TimeSpan DBNotificationPropagationPostponeInterval
        {
            get
            {
                return CloudConfigurationManager
                        .GetConfigurationTimeSpan(
                            ProviderConstants.Databricks.DBNotificationPropagationPostponeIntervalKey,
                            TimeSpan.FromMinutes(10));
            }
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdatingHandler" /> class.
        /// </summary>
        /// <param name="applianceDataProvider">The application data provider.</param>
        /// <param name="accessConnectorDataProvider">accessConnector data provider.</param>
        /// <param name="marketPlacePackageDataProvider">The market place package data provider.</param>
        /// <param name="frontdoorEngine">The frontdoor engine.</param>
        /// <param name="applianceEngine">The appliance engine.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="metadata">The metadata.</param>
        public UpdatingHandler(IApplianceDataProvider applianceDataProvider,
                                IAccessConnectorDataProvider accessConnectorDataProvider,
                                IApplicationPackageDataProvider marketPlacePackageDataProvider,
                                IFrontdoorEngine frontdoorEngine,
                                IApplianceEngine applianceEngine,
                                ILogger logger,
                                ApplianceProvisioningJobMetadata metadata)
        {
            this.ApplianceDataProvider = applianceDataProvider;
            this.AccessConnectorDataProvider = accessConnectorDataProvider;
            this.Metadata = metadata;
            this.MarketPlacePackageDataProvider = marketPlacePackageDataProvider;
            this.Logger = logger;
            this.FrontdoorEngine = frontdoorEngine;
            this.ApplianceEngine = applianceEngine;
            this.VirtualNetworkManager = new VirtualNetworkManager(frontdoorEngine,
                logger.EventSource,
                this.ApplianceEngine.GetDatabricksBlobDataProvider());
            this.StorageAccountManager = new StorageAccountManager(frontdoorEngine,
                this.Logger.EventSource);
            this.WorkspaceTagService = new WorkspaceTagService(frontdoorEngine,
                logger.EventSource);
        }

        /// <summary>
        /// Handles workspace update state.
        /// </summary>
        /// <returns>Returns the JobExecutionResult.</returns>
        public async Task<JobExecutionResult> Execute()
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var isNetworkUpdatePostponeJob = false;

            ApplianceEntity existingEntity = await ApplianceDataProvider
                            .FindAppliance(
                                subscriptionId: Metadata.SubscriptionId,
                                resourceGroupName: Metadata.ResourceGroupName,
                                applianceName: Metadata.ApplianceName)
                            .ConfigureAwait(continueOnCapturedContext: false);
            var databricksBackendApiVersion = existingEntity.GetDatabricksBackendApiVersion(this.Logger.EventSource);
            var publisherPackageId = existingEntity.Properties.PublisherPackageId ?? await this.GetPublisherPackageId(existingEntity);

            var appliancePackage = this.MarketPlacePackageDataProvider
                .FindMarketplaceAppliancePackage(
                    publisherPackageId: publisherPackageId);

            var incomingEntity = new ApplianceEntity();

            incomingEntity.SetPropertiesFromMetadataParameters(this.Metadata);
            incomingEntity.CopyPropertiesFrom(existingEntity);

            var workspaceResourceId = existingEntity.GetFullyQualifiedResourceId();

            this.Logger.LogDebug(
                operationName,
                $"Update existing appliance with Id '{workspaceResourceId}', managed resource group '{existingEntity.Properties.ManagedResourceGroupId}' in ProvisioningState '{existingEntity.Properties.ProvisioningState}'");

            var customerTenantId = RequestCorrelationContext.Current.GetHomeTenantId();
            var publisherTenantId = appliancePackage.TenantId;

            await this.ApplianceEngine
                .CreateManagedResourceGroupIfNotExists(
                    existingEntity: existingEntity,
                    publisherTenantId: publisherTenantId,
                    customerTenantId: customerTenantId,
                    resourceType: this.Metadata.ResourceType,
                    resourceProviderNamespace: this.Metadata.ResourceProviderNamespace)
                .ConfigureAwait(continueOnCapturedContext: false);

            try
            {
                await this.UpdateWorkspaceProperties(existingEntity, appliancePackage, incomingEntity).ConfigureAwait(false);

                await this.ConfigureEncryptionOnWorkspace(existingEntity, appliancePackage, incomingEntity).ConfigureAwait(false);

                this.ConfigureEscOnWorkspace(existingEntity, incomingEntity);
            }
            catch (Exception ex)
            {
                var errorCode = (ex as ServerErrorResponseMessageException)?.ErrorCode ??
                                ErrorResponseCode.WorkspaceUpdateFailed.ToString();

                var failedJobExecutionResult = this.HandleFailedJobExecutionResult(
                    existingEntity.GetFullyQualifiedResourceId(),
                    this.Metadata,
                    errorCode,
                    ex);

                if (failedJobExecutionResult.Status == JobExecutionStatus.Faulted)
                {
                    //Set provisioning state back to succeeded. Since any of the above Update failure will not lead to workspace failed provisioning state.
                    // Update workflow only occurs if the workspace is in success state
                    await this.ApplianceDataProvider
                        .SetApplianceProvisioningState(existingEntity.SubscriptionId,
                            existingEntity.ResourceGroup,
                            existingEntity.Name,
                            ProvisioningState.Succeeded)
                        .ConfigureAwait(continueOnCapturedContext: false);
                }

                return failedJobExecutionResult;
            }

            try
            {
                var dbfsResourceId = Utilities.GetDbfsResourceId(existingEntity.Properties.Parameters, existingEntity.Properties.ManagedResourceGroupId);

                // defaultStorageFirewall Update
                var operationResult = await this.ApplianceEngine
                    .ConfigureDefaultStorageFirewall(
                        incomingEntity,
                        existingEntity.Properties.AccessConnector,
                        existingEntity.Properties.DefaultStorageFirewall,
                        appliancePackage.TenantId,
                        dbfsResourceId)
                    .ConfigureAwait(false);

                if (operationResult.Item1 != null)
                {
                    return operationResult.Item1;
                }

                existingEntity.Properties.AccessConnector = operationResult.Item2;
                existingEntity.Properties.DefaultStorageFirewall = operationResult.Item3;


                var notificationResult = await this.DBWorkspaceUpdateNotificationWrapper(
                            workspaceResourceId,
                            existingEntity,
                            appliancePackage.TenantId,
                            databricksBackendApiVersion,
                            existingEntity.Properties.WorkspaceUrl)
                    .ConfigureAwait(false);

                if (notificationResult != null)
                {
                    // If we got a result from the notification, return it to start polling
                    return notificationResult;
                }

                this.PopulatePropertiesFromAppliancePackage(existingEntity, appliancePackage);

                this.Logger.LogDebug(
                    operationName,
                    $"Completed Notification of workspace update for '{workspaceResourceId}' to downstream, Api-version: {RequestCorrelationContext.Current.ApiVersion}");

                JobExecutionResult jobExecutionResult;
                // Network Update: PIP > NPIP or NPIP > PIP
                if (existingEntity.IsNetworkPropertyChanged(incomingEntity, this.Logger.EventSource))
                {
                    jobExecutionResult = await SetNetworkInfrastructure(existingEntity, incomingEntity).ConfigureAwait(false);

                    if (jobExecutionResult != null)
                    {
                        // persist existingEntity
                        await this
                        .ApplianceDataProvider
                        .ReplaceAppliance(existingEntity)
                        .ConfigureAwait(false);

                        return jobExecutionResult;
                    }

                    await this.ApplianceEngine
                                           .CreateOrUpdateDenyAssignmentsForApplication(
                                               appliancePackage.TenantId,
                                               RequestCorrelationContext.Current.GetHomeTenantId(),
                                               incomingEntity,
                                               this.Metadata.ResourceType,
                                               this.Metadata.ResourceProviderNamespace,
                                               existingEntity.Properties.AccessConnector?.PrincipalId)
                                           .ConfigureAwait(false);

                    jobExecutionResult = await UpdateManagedResourceGroupResources(incomingEntity, existingEntity, appliancePackage).ConfigureAwait(false);

                    if (jobExecutionResult != null)
                    {
                        this.Logger.LogDebug(
                            operationName,
                            $"Saving the workspace current state to backend database");

                        await this
                        .ApplianceDataProvider
                        .ReplaceAppliance(existingEntity)
                        .ConfigureAwait(false);

                        return jobExecutionResult;
                    }

                    existingEntity.SetNetworkPropertiesToAppliance(incomingEntity, this.Logger.EventSource);
                    isNetworkUpdatePostponeJob = true;
                }

                if (isNetworkUpdatePostponeJob)
                {
                    await this
                    .ApplianceDataProvider
                    .ReplaceAppliance(existingEntity)
                    .ConfigureAwait(false);

                    this.Metadata.ResourceOperation = ProvisioningOperation.PostDeploymentWaiting;

                    return new JobExecutionResult
                    {
                        Status = JobExecutionStatus.Postponed,
                        NextExecutionTime = DateTime.UtcNow.Add(this.DBNotificationPropagationPostponeInterval),
                        Message = $"Successfully completed ARM deployment for the workspace '{existingEntity.Name}'. Waiting for the post deployment operations. Postponing job",
                        Details = HttpStatusCode.OK.ToString(),
                        NextMetadata = this.Metadata.ToJson()
                    };
                }
                else
                {
                    existingEntity.Properties.ProvisioningState = ProvisioningState.Succeeded;

                    await this
                    .ApplianceDataProvider
                    .ReplaceAppliance(existingEntity)
                    .ConfigureAwait(false);

                    this.Logger.LogDebug(operationName, $"The workspace '{existingEntity.GetFullyQualifiedResourceId()}' update completed.");

                    this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                    this.Metadata.OperationResponseContent = AsyncOperationResult
                        .GetAsyncOperationResult(ProvisioningState.Succeeded)
                        .ToJToken();

                    return new JobExecutionResult
                    {
                        Status = JobExecutionStatus.Succeeded,
                        Message = $"Successfully provisioned and updated the workspace '{existingEntity.GetFullyQualifiedResourceId()}'. Completing job",
                        Details = HttpStatusCode.OK.ToString(),
                        NextMetadata = this.Metadata.ToJson()
                    };
                }
            }

            catch (Exception ex)
            {
                var defaultErrorMessage = ErrorResponseMessages.WorkspaceUpdateFailed.ToLocalizedMessage(
                    existingEntity.GetFullyQualifiedResourceId(),
                    ex.Message);

                if (ex is ServerErrorResponseMessageException exception)
                {
                    if (exception.ErrorCode.Equals(ErrorResponseCode.PermissionDenied.ToString(),
                            StringComparison.InvariantCultureIgnoreCase))
                    {
                        this.Logger.LogDebug(
                            operationName,
                            $"Failed to Notify workspace update for '{workspaceResourceId}' to RPBackend. Failure : {ex}");

                        // Set provisioning state back to succeeded. Since any of the above Update failure will not lead to workspace failed provisioning state.
                        // Update workflow only occurs if the workspace is in success state
                        await this.ApplianceDataProvider
                            .SetApplianceProvisioningState(existingEntity.SubscriptionId,
                                existingEntity.ResourceGroup,
                                existingEntity.Name,
                                ProvisioningState.Succeeded)
                            .ConfigureAwait(continueOnCapturedContext: false);

                        return this.HandleFailedJobExecutionResult(
                            existingEntity.GetFullyQualifiedResourceId(),
                            this.Metadata,
                            exception.ErrorCode,
                            ex);
                    }
                }

                var failedJobExecutionResult = this.HandleFailedJobExecutionResult(
                    existingEntity.GetFullyQualifiedResourceId(),
                    this.Metadata,
                    ErrorResponseCode.WorkspaceUpdateFailed.ToString(),
                    ex);

                if (failedJobExecutionResult.Status == JobExecutionStatus.Faulted && existingEntity?.Properties != null)
                {
                    await this.ApplianceDataProvider.SetApplianceProvisioningState(
                            existingEntity.SubscriptionId,
                            existingEntity.ResourceGroup,
                            existingEntity.Name,
                            ProvisioningState.Failed)
                        .ConfigureAwait(continueOnCapturedContext: false);
                }

                this.Logger.LogError(
                    operationName: "ApplianceProvisioningJob.UpdatingHandler",
                    format: "Updating resource type '{0}' with id '{1}' failed with deployment operation.",
                    arg0: this.Metadata.ResourceType,
                    arg1: existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType),
                    exception: ex);

                return failedJobExecutionResult;
            }
        }

        /// <summary>
        /// Updates Workspace properties like SKU, AML, Storage & Tags
        /// </summary>
        /// <param name="existingEntity">Existing Workspace appliance object</param>
        /// <param name="appliancePackage">Package</param>
        /// <param name="incomingEntity">Incoming Workspace request</param>
        private async Task UpdateWorkspaceProperties(
            ApplianceEntity existingEntity,
            MarketplaceAppliancePackage appliancePackage,
            ApplianceEntity incomingEntity)
        {
            var updatedSku = incomingEntity.Sku?.TryFromJToken<DatabricksSku>();
            if (updatedSku != null && updatedSku.Name != DatabricksSkuName.NotSpecified)
            {
                existingEntity.Sku = incomingEntity.Sku;
            }

            var updatedBy = GetApplicationClientDetailsEntity();
            existingEntity.Properties.UpdatedBy = updatedBy;

            //Update AML Workspace property
            if (incomingEntity.Properties.Parameters != null &&
                incomingEntity.Properties.Parameters.TryParseAmlWorkspaceId(this.Logger.EventSource, out string amlWorkspaceId))
            {
                existingEntity.SetAmlWorkspaceId(amlWorkspaceId);
            }

            //Update Storage Account (DBFS) SKU
            await SetStorageAccountSku(incomingEntity,
                    existingEntity,
                    appliancePackage.TenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            //Set workspace Tag
            await this.SetWorkspaceTags(existingEntity,
                appliancePackage.TenantId,
                incomingEntity);
        }

        /// <summary>
        /// Update Workspace properties which doesn't disturb provisioning state
        /// </summary>
        /// <param name="existingEntity"></param>
        /// <param name="appliancePackage"></param>
        /// <param name="incomingEntity"></param>
        private async Task ConfigureEncryptionOnWorkspace(
            ApplianceEntity existingEntity,
            MarketplaceAppliancePackage appliancePackage,
            ApplianceEntity incomingEntity)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            //// Managed disk async update
            //// Update disk encryption properties if provided and different from existing properties
            if (incomingEntity.Properties.EncryptionProperties?.EncryptionEntities?.ManagedDisk != null &&
                !incomingEntity.Properties.EncryptionProperties.EncryptionEntities.ManagedDisk.Equals(existingEntity
                    .Properties.EncryptionProperties?.EncryptionEntities?.ManagedDisk))
            {
                await this.HandleManagedDiskEncryptionUpdate(appliancePackage.TenantId,
                    existingEntity,
                    incomingEntity);
            }

            //// Managed services async update
            //// Updated encryption properties related to Managed Services if provided and different from existing properties
            if (incomingEntity.Properties.EncryptionProperties?.EncryptionEntities?.ManagedServices != null &&
                !incomingEntity.Properties.EncryptionProperties.EncryptionEntities.ManagedServices.Equals(
                    existingEntity.Properties.EncryptionProperties?.EncryptionEntities?.ManagedServices))
            {
                existingEntity = this.HandleManagedServicesEncryptionUpdate(existingEntity,
                    incomingEntity);

                this.Logger.LogDebug(operationName,
                    $"Managed Services CMK properties updated. Updated entity: {existingEntity.Properties.ToJson()}");
            }

            //Enable CMK for DBFS
            if (incomingEntity.Properties.Parameters != null)
            {
                await this.EnableManagedIdentityOnStorage(
                        existingEntity.SubscriptionId,
                        appliancePackage.TenantId,
                        existingEntity,
                        incomingEntity)
                    .ConfigureAwait(false);

                await this.ConfigureEncryptionOnStorage(
                        existingEntity.SubscriptionId,
                        appliancePackage.TenantId,
                        existingEntity,
                        incomingEntity)
                    .ConfigureAwait(false);
            }

            //Updating metadata parameters to ensure state of encryption properties is persisted
            this.Metadata.Parameters[ProviderConstants.WorkspaceUpdateParameters.Properties] = incomingEntity.Properties.ToJToken();
        }

        /// <summary>
        /// Updating the ESC properties of the workspace
        /// </summary>
        /// <param name="existingEntity"></param>
        /// <param name="incomingEntity"></param>
        /// <returns></returns>
        private void ConfigureEscOnWorkspace(
            ApplianceEntity existingEntity,
            ApplianceEntity incomingEntity)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            if (incomingEntity.Properties.EnhancedSecurityCompliance != null &&
                incomingEntity.Properties.EnhancedSecurityCompliance != existingEntity.Properties.EnhancedSecurityCompliance)
            {
                existingEntity.Properties.EnhancedSecurityCompliance = incomingEntity.Properties.EnhancedSecurityCompliance;

                this.Logger.LogDebug(operationName,
                    $"ESC properties updated. Updated entity: {existingEntity.Properties.ToJson()}");
            }

            this.Metadata.Parameters[ProviderConstants.WorkspaceUpdateParameters.Properties] = incomingEntity.Properties.ToJToken();
        }

        /// <summary>
        /// Set workspace and managed resource tags
        /// </summary>
        /// <param name="existingEntity">Existing Workspace Object</param>
        /// <param name="tenantId">Publisher tenant ID</param>
        /// <param name="incomingEntity">Incoming request workspace Object</param>
        private async Task SetWorkspaceTags(ApplianceEntity existingEntity,
            string tenantId,
            ApplianceEntity incomingEntity)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            if (!TagUtils.TagsEquals(existingEntity.Tags,
                    incomingEntity.Tags))
            {
                this.Logger.LogDebug(
                    operationName,
                    $"Tag difference '{TagUtils.GetTagsDifferences(existingEntity.Tags, incomingEntity.Tags)?.ToString()}'");

                existingEntity.Tags = incomingEntity.Tags;
                var resourceGroupRequest =
                    ResourceGroupRequestMatch.FromFullyQualifiedId(
                        existingEntity.Properties.ManagedResourceGroupId.Trim());

                await WorkspaceTagService
                    .UpdateWorkspaceTag(
                        incomingEntity.Tags,
                        tenantId,
                        resourceGroupRequest.ResourceGroup,
                        ProviderConstants.Databricks.ResourceProviderNamespace,
                        existingEntity);
            }
        }

        /// <summary>
        /// Set DBFS Storage Account based on incoming PUT request storage sku parameter. It will be no-op in case storage sku parameter is missing.
        /// </summary>
        /// <param name="incomingEntity">Incoming workspace request entity</param>
        /// <param name="existingEntity">Existing workspace entity</param>
        /// <param name="tenantId">Tenant Id</param>
        private async Task SetStorageAccountSku(
            ApplianceEntity incomingEntity,
            ApplianceEntity existingEntity,
            string tenantId)
        {
            if (incomingEntity.Properties.Parameters == null)
            {
                return;
            }

            incomingEntity.Properties.Parameters.TryParseDatabricksPropertiesParameter(
                ProviderConstants.Databricks.StorageAccountSkuNameParameter,
                this.Logger.EventSource,
                out string incomingStorageAccountSku);

            existingEntity.Properties.Parameters.TryParseDatabricksPropertiesParameter(
                    ProviderConstants.Databricks.StorageAccountSkuNameParameter,
                    this.Logger.EventSource,
                    out string existingStorageAccountSkuName);

            if (!string.IsNullOrWhiteSpace(incomingStorageAccountSku) && !string.IsNullOrWhiteSpace(existingStorageAccountSkuName))
            {
                if (!incomingStorageAccountSku.Trim().Equals(existingStorageAccountSkuName.Trim(), StringComparison.InvariantCultureIgnoreCase))
                {
                    existingEntity.SetStorageAccountSku(incomingStorageAccountSku);
                    await this.StorageAccountManager.PatchStorageAsync(existingEntity,
                        incomingStorageAccountSku,
                        tenantId ?? RequestCorrelationContext.Current.GetHomeTenantId(),
                        this.Metadata.ResourceType,
                        this.Metadata.ResourceProviderNamespace);
                }
            }
        }

        /// <summary>
        /// Deploy Managed Resource Group Resources
        /// </summary>
        /// <param name="incomingEntity">Incoming Request workspace entity</param>
        /// <param name="existingEntity">Existing Workspace entity</param>
        /// <param name="appliancePackage">Appliance Package</param>
        /// <returns></returns>
        private async Task<JobExecutionResult> UpdateManagedResourceGroupResources(
            ApplianceEntity incomingEntity,
            ApplianceEntity existingEntity,
            MarketplaceAppliancePackage appliancePackage)
        {
            JobExecutionResult jobExecution = default;
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            //// ARM template
            try
            {
                this.Logger.LogDebug(operationName, $"Allow MRG resources update");

                JObject resourceTagsParameter =
                    TagUtils.GetTagsParameterForInitialResources(incomingEntity.Tags);

                this.Logger.LogDebug(operationName: operationName,
                    format: "Provisioning initial resources with tag parameter: {0}",
                    arg0: resourceTagsParameter.ToString());

                incomingEntity.Properties.Parameters[TagUtils.MainTemplateTagsParameterName] =
                    resourceTagsParameter;

                incomingEntity.Properties.Parameters = incomingEntity.Properties.Parameters.CoalesceEnumerable().ToInsensitiveDictionary(
                    keySelector: parameter => parameter.Key,
                    elementSelector: parameter => JToken.FromObject(new Dictionary<string, object>
                    {
                            { "value", parameter.Value.SelectToken("value") }
                    }));

                incomingEntity.Properties.Parameters.TryParseDatabricksPropertiesParameter(
                    ProviderConstants.Databricks.StorageAccountNameParameter,
                    this.Logger.EventSource,
                    out string incomingStorageAccountName);

                existingEntity.Properties.Parameters.TryParseDatabricksPropertiesParameter(
                    ProviderConstants.Databricks.StorageAccountNameParameter,
                    this.Logger.EventSource,
                    out string existingStorageAccountName);

                if (string.IsNullOrWhiteSpace(incomingStorageAccountName))
                {
                    incomingEntity.Properties.Parameters[ProviderConstants.Databricks
                        .StorageAccountNameParameter] = new JObject()
                    {
                        new JProperty(ProviderConstants.Storage.ParameterValue,
                            existingStorageAccountName)
                    };
                }

                incomingEntity.Properties.Parameters.Remove(ProviderConstants.Storage.Encryption);

                //These properties are allowed to be specified in api version before 2025-02-01-preview but not used so removing them
                incomingEntity.Properties.Parameters.Remove(ProviderConstants.Databricks.LoadBalancerIdProperty);
                incomingEntity.Properties.Parameters.Remove(ProviderConstants.Databricks.LoadBalancerBackendPoolNameProperty);

                this.Logger.LogDebug(
                    operationName: operationName,
                    format: "Deploying parameter: {0}",
                    arg0: incomingEntity.Properties.Parameters?.ToJson());

                DeploymentResponse deploymentResponse = await this.ApplianceEngine
                    .CreateApplianceDeployment(
                        appliance: existingEntity,
                        templateParameters: incomingEntity.Properties.Parameters,
                        appliancePackage: appliancePackage,
                        publisherTenantId: appliancePackage.TenantId,
                        resourceType: this.Metadata.ResourceType,
                        resourceProviderNamespace: this.Metadata.ResourceProviderNamespace,
                        DeploymentMode.Incremental)
                    .ConfigureAwait(continueOnCapturedContext: false);

                if (deploymentResponse != null
                    && deploymentResponse.OutputDefinition != null
                    && deploymentResponse.OutputDefinition.Properties != null)
                {
                    InsensitiveDictionary<JToken> parameters = deploymentResponse.OutputDefinition.Properties.Parameters;
                    Utilities.RemoveDeploymentParameters(parameters, this.Logger.EventSource);
                }

                this.Metadata.OperationTrackingUri = deploymentResponse.AzureAsyncOperationUri;
                this.Metadata.ResourceOperation = ProvisioningOperation.AzureAsyncOperationWaiting;

                jobExecution = new JobExecutionResult
                {
                    Status = JobExecutionStatus.Postponed,
                    NextExecutionTime = DateTime.UtcNow.Add(this.PostponeInterval),
                    Message = string.Format("Successfully submitted ARM deployment. Postponing job"),
                    Details = HttpStatusCode.OK.ToString(),
                    NextMetadata = this.Metadata.ToJson()
                };
            }
            catch (Exception exception)
            {
                if (exception is DeploymentOperationException operationException)
                {
                    this.Logger.LogDebug(
                        operationName: operationName,
                        format:
                        "Provisioning resource type '{0}' with id '{1}' failed with deployment operation exception '{2}'.",
                        arg0: this.Metadata.ResourceType,
                        arg1: existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType),
                        arg2: Utilities.FlattenException(operationException));

                    if (Utilities.IsDeploymentOperationExceptionRetryable(
                            operationException,
                            this.Metadata.CurrentDeploymentOperationExceptionRetryableErrorCount,
                            this.MaxRetries))
                    {
                        this.Metadata.CurrentDeploymentOperationExceptionRetryableErrorCount++;

                        return this.PostponeJob(
                            message: operationException.Message,
                            this.Metadata,
                            delay: this.PostponeInterval);
                    }

                    var ex = exception as DeploymentOperationException;
                    this.Metadata.SLIErrorInfo = SLIErrorInfo.GetSLIErrorInfo(ex.HttpStatus, ex.ErrorCode);
                }
                else
                {
                    this.Logger.LogDebug(
                        operationName: operationName,
                        format: "Provisioning resource type '{0}' with id '{1}' failed with generic exception '{2}'.",
                        arg0: this.Metadata.ResourceType,
                        arg1: existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType),
                        arg2: Utilities.FlattenException(exception));

                    if (this.Metadata.CurrentDeploymentOperationExceptionRetryableErrorCount < this.MaxRetries)
                    {
                        this.Metadata.CurrentDeploymentOperationExceptionRetryableErrorCount++;
                        return this.PostponeJob(
                             message: exception.Message,
                             metadata: this.Metadata,
                             delay: this.PostponeInterval);
                    }
                }

                this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                this.Metadata.OperationResponseContent = AsyncOperationResult
                    .GetAsyncOperationResult(
                        provisioningState: ProvisioningState.Failed,
                        errorCode: ErrorResponseCode.ApplianceProvisioningFailed.ToString(),
                        message: exception.Message)
                    .ToJToken();

                await this.ApplianceDataProvider
                    .SetApplianceProvisioningState(
                        subscriptionId: this.Metadata.SubscriptionId,
                        resourceGroupName: this.Metadata.ResourceGroupName,
                        applianceName: this.Metadata.ApplianceName,
                        provisioningState: ProvisioningState.Failed)
                    .ConfigureAwait(continueOnCapturedContext: false);

                jobExecution = new JobExecutionResult
                {
                    Status = JobExecutionStatus.Completed,
                    Message = "Appliance provisioning job completed with deployment failures.",
                    NextMetadata = this.Metadata.ToJson()
                };
            }

            return jobExecution;
        }

        /// <summary>
        /// Re-creates the network intent policies.
        /// </summary>
        /// <param name="existingEntity">The appliance from database.</param>
        /// <param name="incomingEntity">A set of information passed by the user during update.</param>
        /// <returns>Return the job execution result.</returns>
        private async Task<JobExecutionResult> SetNetworkInfrastructure(
            ApplianceEntity existingEntity,
            ApplianceEntity incomingEntity)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            JobExecutionResult jobExecutionResult = default;

            #region Unprepare subnet

            var unPrepareResponse = await this.VirtualNetworkManager
                .UnPrepareSubnets(
                    ApplianceDataProvider,
                    existingEntity,
                    existingEntity.Properties.Parameters,
                    RequestCorrelationContext.Current.GetHomeTenantId(),
                    this.Metadata.ResourceType,
                    this.Metadata.ResourceProviderNamespace)
                .ConfigureAwait(false);

            if (unPrepareResponse.Status == OperationStatus.Failed)
            {
                await this.ApplianceDataProvider
                    .SetApplianceProvisioningState(
                        subscriptionId: this.Metadata.SubscriptionId,
                        resourceGroupName: this.Metadata.ResourceGroupName,
                        applianceName: this.Metadata.ApplianceName,
                        provisioningState: ProvisioningState.Failed)
                    .ConfigureAwait(continueOnCapturedContext: false);

                this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                this.Metadata.OperationResponseContent = AsyncOperationResult.GetAsyncOperationResult(
                        ProvisioningState.Failed,
                        unPrepareResponse.ErrorResponseMessage?.Error)
                    .ToJToken();

                string errorMessage =
                    "Failed to provision appliance resource while removing network intent policies from subnets. Job faulted.";
                this.Logger.LogDebug(operationName,
                    errorMessage);

                jobExecutionResult = new JobExecutionResult
                {
                    Status = JobExecutionStatus.Faulted,
                    Message = errorMessage,
                    Details = HttpStatusCode.InternalServerError.ToString(),
                    NextMetadata = this.Metadata.ToJson()
                };

                return jobExecutionResult;
            }

            this.Logger.LogDebug(
                operationName,
                $"Un-prepare subnets for the workspace is complete with status {unPrepareResponse.Status}");

            #endregion

            #region Prepare subnet

            var prepareResponse = await this.VirtualNetworkManager
                .PrepareSubnets(
                    ApplianceDataProvider,
                    incomingEntity,
                    incomingEntity.Properties.Parameters,
                    RequestCorrelationContext.Current.GetHomeTenantId(),
                    this.Metadata.ResourceType,
                    this.Metadata.ResourceProviderNamespace,
                    this.Metadata.CurrentRetryableErrorCount)
                .ConfigureAwait(false);

            if (prepareResponse.Status == OperationStatus.Failed)
            {
                this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                this.Metadata.OperationResponseContent = AsyncOperationResult.GetAsyncOperationResult(
                        ProvisioningState.Failed,
                        prepareResponse.ErrorResponseMessage?.Error)
                    .ToJToken();

                string errorMessage =
                    "Please refer https://aka.ms/nipfailuretsg for troubleshooting details, Failed to provision appliance resource while subnets preparation. Job faulted.";

                this.Logger.LogError(operationName,
                    errorMessage);

                await this
                    .ApplianceDataProvider
                    .SetApplianceProvisioningState(
                        subscriptionId: this.Metadata.SubscriptionId,
                        resourceGroupName: this.Metadata.ResourceGroupName,
                        applianceName: this.Metadata.ApplianceName,
                        provisioningState: ProvisioningState.Failed)
                    .ConfigureAwait(continueOnCapturedContext: false);

                jobExecutionResult = new JobExecutionResult
                {
                    Status = JobExecutionStatus.Faulted,
                    Message = errorMessage,
                    Details = HttpStatusCode.InternalServerError.ToString(),
                    NextMetadata = this.Metadata.ToJson()
                };

                return jobExecutionResult;
            }

            if (prepareResponse.Status == OperationStatus.Postponed)
            {
                string errorCode = prepareResponse.ErrorResponseMessage?.Error?.Code ??
                                   ErrorResponseCode.PrepareSubnetsInternalError.ToString();
                this.Logger.LogDebug(
                    operationName,
                    $"A retry-able error occurred while preparing subnets for the workspace'{incomingEntity.GetFullyQualifiedId(this.Metadata.ResourceType)}'. Status {prepareResponse.Status.ToString()}. Error code: {errorCode}");

                throw new VirtualNetworkInjectionOperationException(
                    HttpUtility.RetryWithStatusCode,
                    errorCode,
                    prepareResponse.ErrorResponseMessage?.Error?.Message ??
                    ErrorResponseMessages.PrepareSubnetsInternalError.ToLocalizedMessage());
            }

            this.Logger.LogDebug(operationName,
                $"Preparing subnets for the workspace is complete with status {prepareResponse.Status}");

            #endregion

            return jobExecutionResult;
        }

        /// <summary>
        /// Configuring Encryption Settings on Managed Storage account
        /// </summary>
        /// <param name="subscriptionId">subscription id</param>
        /// <param name="publisherTenantId">Publisher tenant id</param>
        /// <param name="existingEntity">appliance entity</param>
        /// <param name="incomingEntity">appliance input</param>
        /// <returns>returns a task</returns>
        private async Task ConfigureEncryptionOnStorage(
            string subscriptionId,
            string publisherTenantId,
            ApplianceEntity existingEntity,
            ApplianceEntity incomingEntity)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            string managedResourceGroupName = ResourceGroupRequestMatch
                .FromFullyQualifiedId(incomingEntity.Properties.ManagedResourceGroupId.Trim())
                .ResourceGroup;
            existingEntity.Properties.Parameters.TryParseDatabricksPropertiesParameter(
                ProviderConstants.Databricks.StorageAccountNameParameter,
                this.Logger.EventSource,
                out string storageAccountName);

            if (!incomingEntity.Properties.Parameters.ContainsKey(ProviderConstants.Storage.Encryption))
            {
                this.Logger.LogDebug(operationName,
                    $"No need to Configure Encryption Settings for storage account {storageAccountName} under Managed Resource Group: '{managedResourceGroupName}'.");

                if (existingEntity.Properties.Parameters.CoalesceDictionary()
                        .TryGetValue(ProviderConstants.Storage.Encryption,
                            out var _encryption))
                {
                    incomingEntity.Properties.Parameters[ProviderConstants.Storage.Encryption] =
                        _encryption;
                }

                return;
            }

            Utilities.TryParseDatabricksPropertiesParameter(incomingEntity.Properties.Parameters,
                ProviderConstants.Storage.Encryption,
                this.Logger.EventSource,
                out EncryptionDefinition encryptionDefinition);

            this.Logger.LogDebug(operationName,
                $"Entering Configure Encryption Settings for storage account {storageAccountName} under Managed Resource Group: '{managedResourceGroupName}'. KeySource {encryptionDefinition.KeySource}");

            JObject encryptionSettings;
            StorageDefinition storageUpdateRequest;
            if (encryptionDefinition.KeySource.EqualsInsensitively(ProviderConstants.Storage.KeySourceDefault))
            {
                this.Logger.LogDebug(operationName,
                    $"Reverting Encryption Configuration Settings for Managed Resource Group: '{managedResourceGroupName}' to default.");
                storageUpdateRequest = new StorageDefinition()
                {
                    Properties = new JObject(
                        new JProperty(
                            ProviderConstants.Storage.Encryption,
                            new JObject
                            {
                                new JProperty(
                                    ProviderConstants.Storage.KeySource,
                                    ProviderConstants.Storage.KeySourceStorage)
                            }))
                };
                encryptionSettings = new JObject()
                {
                    new JProperty(ProviderConstants.Storage.ParameterType,
                        ProviderConstants.Storage.ParameterObjectType),
                    new JProperty(
                        ProviderConstants.Storage.ParameterValue,
                        new JObject(
                            new JProperty(
                                ProviderConstants.Storage.KeySource,
                                ProviderConstants.Storage.KeySourceDefault)))
                };
            }
            else
            {
                this.Logger.LogDebug(operationName,
                    $"Setting Encryption Configuration for storage account {storageAccountName} under Managed Resource Group: '{managedResourceGroupName}' to Microsoft.KeyVault");
                storageUpdateRequest = new StorageDefinition()
                {
                    Properties = new JObject(
                        new JProperty(
                            ProviderConstants.Storage.Encryption,
                            new JObject
                            {
                                new JProperty(
                                    ProviderConstants.Storage.KeySource,
                                    ProviderConstants.Storage.KeySourceKeyVault),
                                new JProperty(
                                    ProviderConstants.Storage.KeyVaultProperties,
                                    new JObject(
                                        new JProperty(
                                            ProviderConstants.Storage.KeyVaultUri,
                                            encryptionDefinition.KeyVaultUri),
                                        new JProperty(
                                            ProviderConstants.Storage.KeyVaultKeyName,
                                            encryptionDefinition.KeyName),
                                        new JProperty(
                                            ProviderConstants.Storage.KeyVaultKeyVersion,
                                            encryptionDefinition.KeyVersion ?? string.Empty)))
                            }))
                };
                encryptionSettings = new JObject()
                {
                    new JProperty(ProviderConstants.Storage.ParameterType,
                        ProviderConstants.Storage.ParameterObjectType),
                    new JProperty(
                        ProviderConstants.Storage.ParameterValue,
                        new JObject(
                            new JProperty(ProviderConstants.Storage.KeySource,
                                ProviderConstants.Storage.KeySourceKeyVault),
                            new JProperty(
                                ProviderConstants.Storage.KeyVaultUri,
                                encryptionDefinition.KeyVaultUri),
                            new JProperty(ProviderConstants.Storage.KeyNameKey,
                                encryptionDefinition.KeyName),
                            new JProperty(ProviderConstants.Storage.KeyVaultKeyVersion,
                                encryptionDefinition.KeyVersion ?? string.Empty)))
                };
            }

            try
            {
                HttpResponseMessage storageEncryptionResponse = await this.FrontdoorEngine
                    .UpdateStorageEncryptionWithByok(
                        subscriptionId,
                        managedResourceGroupName,
                        storageAccountName,
                        publisherTenantId,
                        ProviderConstants.Databricks.ResourceProviderNamespace,
                        storageUpdateRequest)
                    .ConfigureAwait(false);

                existingEntity.Properties.Parameters[ProviderConstants.Storage.Encryption] = encryptionSettings;
                incomingEntity.Properties.Parameters[ProviderConstants.Storage.Encryption] = encryptionSettings;

                this.Logger.LogDebug(operationName,
                    $"Successfully updated storage account encryption for workspaceId - '{existingEntity.GetFullyQualifiedResourceId()}'.");
            }
            catch (StorageOperationException exception)
            {
                this.Logger.LogError(operationName,
                    $"Failed to update storage account encryption setting in managed resource group - '{managedResourceGroupName}'.");

                throw new ErrorResponseMessageException(
                    HttpStatusCode.Conflict,
                    ErrorResponseCode.StorageAccountOperationFailed,
                    ErrorResponseMessages.StorageAccountUpdateFailed.ToLocalizedMessage(storageAccountName,
                        exception.Message));
            }
        }

        /// <summary>
        /// Enable Managed identity if not already enabled
        /// </summary>
        /// <param name="subscriptionId">subscription Id</param>
        /// <param name="publisherTenantId">publisher tenant Id</param>
        /// <param name="existingEntity">appliance entity</param>
        /// <param name="incomingEntity">input appliance</param>
        /// <returns>returns a task to enable MSI operation</returns>
        private async Task EnableManagedIdentityOnStorage(
            string subscriptionId,
            string publisherTenantId,
            ApplianceEntity existingEntity,
            ApplianceEntity incomingEntity)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            string workspaceId = existingEntity.GetFullyQualifiedResourceId();
            string managedResourceGroupName = ResourceGroupRequestMatch
                .FromFullyQualifiedId(incomingEntity.Properties.ManagedResourceGroupId.Trim())
                .ResourceGroup;
            existingEntity.Properties.Parameters.TryParseDatabricksPropertiesParameter(
                ProviderConstants.Databricks.StorageAccountNameParameter,
                this.Logger.EventSource,
                out string storageAccountName);

            Uri storageUpdateResourceUri = UriTemplateEngine.GetStorageGetPropertiesRequestUri(
                this.FrontdoorEngine.FrontdoorEndpointUri,
                subscriptionId,
                managedResourceGroupName,
                storageAccountName,
                this.FrontdoorEngine.StorageApiVersion);

            bool doesInputHasPrepareEncryption =
                incomingEntity.Properties.Parameters.ContainsKey(ProviderConstants.Storage.PrepareEncryption);
            bool doesStorageIdentityNotExist =
                string.IsNullOrEmpty(existingEntity.Properties.StorageAccountIdentityEntity?.PrincipalId);

            this.Logger.LogDebug(
                operationName,
                $"doesInputHasPrepareEncryption: {doesInputHasPrepareEncryption}, doesStorageIdentityNotExist: {doesStorageIdentityNotExist}");

            if (doesInputHasPrepareEncryption &&
                incomingEntity.Properties.Parameters.TryParseDatabricksPropertiesParameter(
                    ProviderConstants.Storage.PrepareEncryption,
                    this.Logger.EventSource,
                    out bool prepareEncryption) &&
                prepareEncryption &&
                doesStorageIdentityNotExist)
            {
                try
                {
                    this.Logger.LogDebug(
                        operationName,
                        $"Enabling storage account MSI for the workspace '{workspaceId}' using Publisher TenantId: {publisherTenantId}");

                    StorageDefinition storageUpdateRequest = new StorageDefinition()
                    {
                        Identity = new JObject()
                        {
                            new JProperty(ProviderConstants.Storage.ParameterType,
                                ProviderConstants.Storage.SystemAssigned)
                        }
                    };

                    StorageDefinition storageAccountDefinition = await this.FrontdoorEngine
                        .CallFrontdoor(
                            new HttpMethod("PATCH"),
                            storageUpdateResourceUri,
                            ProviderConstants.Databricks.ResourceProviderNamespace,
                            publisherTenantId,
                            storageUpdateRequest)
                        .ConfigureAwait(false);

                    if (storageAccountDefinition == null)
                    {
                        this.Logger.LogDebug(
                            operationName,
                            $"The enable managed identity failed for workspace '{workspaceId}'");
                        throw new ErrorResponseMessageException(
                            HttpStatusCode.InternalServerError,
                            ErrorResponseCode.EnableMsiForStorageInternalError,
                            ErrorResponseMessages.EnableMsiForStorageInternalError.ToLocalizedMessage(workspaceId));
                    }

                    existingEntity.PopulateStorageAccountIdentityEntity(storageAccountDefinition);
                    incomingEntity.PopulateStorageAccountIdentityEntity(storageAccountDefinition);

                    this.Logger.LogDebug(operationName,
                        $"Enable managed identity successful for dbfs storage of workspace '{workspaceId}'.");
                }
                catch (ServerErrorResponseMessageException exception)
                {
                    this.Logger.LogError(
                        operationName,
                        $"Error occurred while enabling MSI for managed storage account of workspaceId '{workspaceId}'. Exception:{Utilities.FlattenException(exception)}");

                    throw new ErrorResponseMessageException(
                        HttpStatusCode.Conflict,
                        ErrorResponseCode.EnableMsiForStorageAccountError,
                        ErrorResponseMessages.InvalidRequestContent.ToLocalizedMessage(exception.Message));
                }
            }
            else
            {
                if (!doesInputHasPrepareEncryption &&
                    existingEntity.Properties.Parameters.CoalesceDictionary()
                        .TryGetValue(ProviderConstants.Storage.PrepareEncryption,
                            out var _prepareEncryption))
                {
                    incomingEntity.Properties.Parameters[ProviderConstants.Storage.PrepareEncryption] =
                        _prepareEncryption;
                }

                if (existingEntity.Properties.StorageAccountIdentityEntity != null)
                {
                    incomingEntity.Properties.StorageAccountIdentityEntity =
                        existingEntity.Properties.StorageAccountIdentityEntity;
                }
            }

            this.Logger.LogDebug(operationName,
                $"Finished enabling MSI on Storage account!!");
        }

        /// <summary>
        /// Calls DB for the workspace UPDATE notification.
        /// </summary>
        /// <param name="workspaceId">The workspace Id.</param>
        /// <param name="notification">The notification object.</param>
        /// <param name="customerTenantId"> The customer tenant id</param>
        /// <param name="apiVersion">The API version.</param>
        /// <param name="workspaceUrl">The workspace URL</param>
        /// <returns>returns the DB response.</returns>
        private async Task<JobExecutionResult> DBWorkspaceUpdateNotificationWrapper(
            string workspaceId,
            ApplianceEntity notification,
            string customerTenantId,
            string apiVersion,
            string workspaceUrl)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            (Uri baseUri, string audience, bool useArm) = await this.FrontdoorEngine
                   .GetDatabricksAccountApiEnpointAndAudience(customerTenantId, this.Metadata.SubscriptionId);
            // Skips the workspace PUT/ PATCH notification.
            if (!Utilities.IsWorkspaceNotificationEnabled(apiVersion))
            {
                this.Logger.LogDebug(
                    operationName,
                    $"SKIP: DB Workspace Notification (config/ AFEC) on WorkspaceId : '{workspaceId}'");

                return null;
            }

            // Skips the workspace PUT/ PATCH notification if WorkspaceUrl is null or empty.
            if (string.IsNullOrEmpty(workspaceUrl))
            {
                this.Logger.LogDebug(
                    operationName,
                    $"SKIP: DB Workspace Notification (empty workspace url) on WorkspaceId : '{workspaceId}'");

                return null;
            }

            try
            {
                string amlWorkspaceId = null;

                if (useArm)
                {
                    amlWorkspaceId = notification.ToDbWorkspace()?.Update?.Properties?.Parameters?.AmlWorkspaceId?.Value<string>("value");
                }
                else
                {
                    amlWorkspaceId = DatabricksWorkspace.FromApplianceEntity(notification)?.Properties?.Parameters?.AmlWorkspaceId?.Value<string>("value");
                }
                // Skips the workspace PUT/ PATCH notification if linked AML Workspace Id doesn't exist.
                this.Logger.LogDebug(
                        operationName,
                        $"DB Workspace Notification - Linked AML Workspace Id : '{amlWorkspaceId}'");

                if (!string.IsNullOrEmpty(amlWorkspaceId))
                {
                    JToken amlWorkspace = await this.FrontdoorEngine
                                            .GetAmlWorkspace(customerTenantId, amlWorkspaceId, ProviderConstants.Databricks.ResourceProviderNamespace)
                                            .ConfigureAwait(false);

                    if (amlWorkspace == default(JToken))
                    {
                        this.Logger.LogError(
                            operationName,
                            $"DB Workspace Notification - Linked AML Workspace Id : '{amlWorkspaceId}' does not exist.");

                        throw new ErrorResponseMessageException(
                            HttpStatusCode.Conflict,
                            ErrorResponseCode.LinkedAmlWorkspaceDoesNotExist,
                            ErrorResponseMessages.LinkedAmlWorkspaceDoesNotExist.ToLocalizedMessage(amlWorkspaceId));
                    }
                }
                var payload = useArm ?
                                   JToken.FromObject(notification.ToDbWorkspace()) :
                                   JToken.FromObject(DatabricksWorkspace.FromApplianceEntity(notification));
                this.Logger.LogDebug(
                    operationName,
                    $"[Feature Flag selection]START: DB Workspace {(useArm ? "PATCH" : "PUT")} Notification on WorkspaceId: '{workspaceId}'" +
                    $"Using {(useArm ? "ARM" : "AccountApi")}");
                await DatabricksAccountsManagerUtils.SendDBNotificationWithFlagControl(
                     this.FrontdoorEngine,
                     payload,
                     useArm ? new HttpMethod("PATCH") : new HttpMethod("PUT"),
                     customerTenantId,
                     useArm,
                     workspaceId,
                     baseUri,
                     apiVersion,
                     audience).ConfigureAwait(false);

                if (!useArm)
                {
                    this.Metadata.AccountApiPollingState = AccountApiPollingState.WaitingForUpdate;
                    this.Metadata.AccountApiOperationId = workspaceId;
                    this.Metadata.AccountApiOperationStartTime = DateTime.UtcNow;
                    this.Metadata.AccountApiBaseUri = baseUri;
                    this.Metadata.AccountApiAudience = audience;
                    this.Metadata.ResourceOperation = ProvisioningOperation.AccountApiPolling;

                    // Set the provisioning state to Updating for job-based polling
                    notification.Properties.ProvisioningState = ProvisioningState.Updating;

                    await this.ApplianceDataProvider
                        .ReplaceAppliance(notification)
                        .ConfigureAwait(false);

                    return new JobExecutionResult
                    {
                        Status = JobExecutionStatus.Postponed,
                        NextExecutionTime = DateTime.UtcNow.Add(TimeSpan.FromSeconds(FrontdoorEngine.AccountApiPollingRetryInterval)),
                        Message = $"Waiting for Account API update operation to complete for workspace '{workspaceId}'",
                        NextMetadata = this.Metadata.ToJson()
                    };
                }

                return null;
            }
            catch (ErrorResponseMessageException errorResponseMessageException) when (errorResponseMessageException.ErrorCode == ErrorResponseCode.LinkedAmlWorkspaceDoesNotExist)
            {
                throw errorResponseMessageException;
            }
            catch (ServerErrorResponseMessageException)
            {
                throw;
            }
            catch (Exception ex)
            {
                var defaultErrorMessage = ErrorResponseMessages.WorkspaceUpdateFailed.ToLocalizedMessage(workspaceId, ex.Message);

                throw new ErrorResponseMessageException(
                    HttpStatusCode.Conflict,
                    ErrorResponseCode.WorkspacePatchFailed,
                    ErrorResponseMessages.WorkspacePatchFailed.ToLocalizedMessage(workspaceId));
            }
        }

        /// <summary>
        /// Populates appliance properties from an associated appliance package.
        /// </summary>
        /// <param name="applianceEntity">The appliance entity.</param>
        /// <param name="appliancePackage">The application package.</param>
        private void PopulatePropertiesFromAppliancePackage(ApplianceEntity applianceEntity, AppliancePackage appliancePackage)
        {
            applianceEntity.Properties.Authorizations = appliancePackage.Authorizations?.Select(auth => new ApplicationAuthorizationEntity()
            {
                PrincipalId = auth.PrincipalId,
                RoleDefinitionId = auth.RoleDefinitionId
            }).ToArray();
        }

        /// <summary>
        /// Get Publisher Package Id.
        /// </summary>
        /// <param name="applianceEntity">The appliance entity.</param>
        /// <returns>Returns API version.</returns>
        private async Task<string> GetPublisherPackageId(ApplianceEntity applianceEntity)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.Logger.LogDebug(operationName, "Getting the publisher package id for the workspace");

            Dictionary<string, string> existingMappings = GetValidatedPublisherPackageIdMappings();

            string apiVersion = ProviderConstants.ApiVersion20180401;

            if (this.IsDevEnvironment)
            {
                // Note: Bypass api-version for development environment.
                return apiVersion;
            }

            try
            {
                List<string> registeredFeatures = await this.FrontdoorEngine.GetRegisteredFeaturesInSubscription(
                        RequestCorrelationContext.Current.GetHomeTenantId(),
                        applianceEntity.SubscriptionId,
                        ProviderConstants.Databricks.ResourceProviderNamespace).ConfigureAwait(false);

                if (applianceEntity.IsApplianceEntityFedRamp(this.Logger.EventSource))
                {
                    this.Logger.LogDebug(operationName, "Workspace is FedRamp enabled");
                    string publisherPackageId = existingMappings[ProviderConstants.ApiVersion20180401];
                    this.Logger.LogDebug(operationName, $"Returning publisher package id {publisherPackageId}");
                    return publisherPackageId;
                }
                else if (registeredFeatures.Contains(ProviderConstants.Databricks.MicrosoftDatabricksDevControlPlaneFeature))
                {
                    this.Logger.LogDebug(operationName, "The workspace is in a dev subscription.");
                    apiVersion = ProviderConstants.ApiVersion20180301;
                }
                else if (registeredFeatures.Contains(ProviderConstants.Databricks.MicrosoftDatabricksStagingControlPlaneFeature))
                {
                    this.Logger.LogDebug(operationName, "The workspace is in a staging subscription.");
                    apiVersion = ProviderConstants.ApiVersion20180315;
                }
            }
            catch (Exception exception)
            {
                this.Logger.LogDebug(operationName, $"Error getting Databricks feature  for subscription '{applianceEntity.SubscriptionId}'. Exception: {Utilities.FlattenException(exception)}");
                throw;
            }

            this.Logger.LogDebug(operationName, $"Api version for the purposes of identifying the publisher package id is {apiVersion}");
            return existingMappings[apiVersion];
        }

        /// <summary>
        /// Gets the application client details
        /// </summary>
        private static ApplicationClientDetailsEntity GetApplicationClientDetailsEntity()
        {
            return new ApplicationClientDetailsEntity
            {
                Oid = RequestCorrelationContext.Current.PrincipalOid,
                Puid = RequestCorrelationContext.Current.GetPrincipalLegacyPuid(),
                ApplicationId = RequestCorrelationContext.Current.ClientAppId
            };
        }

        /// <summary>
        /// Get validated <c>PublisherPackageId</c> mappings from configuration.
        /// </summary>
        public Dictionary<string, string> GetValidatedPublisherPackageIdMappings()
        {
            string[] publisherPackageIdMappings = CloudConfigurationManager.GetMultivaluedConfiguration(
                "Microsoft.WindowsAzure.ResourceStack.Providers.Databricks.Workspace.PublisherPackageIdMappings");

            return publisherPackageIdMappings
                .ToDictionary(
                    keySelector: publisherPackageIdMapping => publisherPackageIdMapping.SplitRemoveEmpty('#')[0],
                    elementSelector: publisherPackageIdMapping => publisherPackageIdMapping.SplitRemoveEmpty('#')[1]);
        }

        private ApplianceEntity HandleManagedServicesEncryptionUpdate(
            ApplianceEntity existingEntity,
            ApplianceEntity incomingEntity)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.Logger.LogDebug(operationName, $"Updating Managed Services encryption properties of workspace - {existingEntity.Properties.ToJson()}");

            try
            {
                var existingEncryptionProperties = existingEntity.Properties.EncryptionProperties;

                if (existingEncryptionProperties == null)
                {
                    existingEncryptionProperties = incomingEntity.Properties.EncryptionProperties;
                }
                else if (existingEncryptionProperties.EncryptionEntities == null)
                {
                    existingEncryptionProperties.EncryptionEntities = incomingEntity.Properties.EncryptionProperties.EncryptionEntities;
                }
                else
                {
                    existingEncryptionProperties.EncryptionEntities.ManagedServices = incomingEntity.Properties.EncryptionProperties.EncryptionEntities.ManagedServices;
                }

                existingEntity.Properties.EncryptionProperties = existingEncryptionProperties;
                incomingEntity.Properties.EncryptionProperties = existingEncryptionProperties;
            }
            catch (Exception e)
            {
                this.Logger.LogDebug(operationName, $"Failed to update Managed Services encryption of entity - {e.Message}");
            }

            return existingEntity;
        }

        #region Managed Disk Encryption Update

        /// <summary>
        /// Handle workspace update for Managed Disk Encryption
        /// </summary>
        /// <param name="publisherTenantId">publisher tenant Id</param>
        /// <param name="existingEntity">appliance entity</param>
        /// <param name="incomingEntity">input appliance</param>
        /// <returns>returns a task to enable MSI operation</returns>
        private async Task HandleManagedDiskEncryptionUpdate(
            string publisherTenantId,
            ApplianceEntity existingEntity,
            ApplianceEntity incomingEntity)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            // validate key details - throw error if not proper - update fails
            // non cmk to cmk
            // cmk exists, update key or update rotation to latest enabled bool
            // make PUT request with DES details

            try
            {
                var managedResourceGroupName = ResourceGroupRequestMatch.FromFullyQualifiedId(incomingEntity.Properties.ManagedResourceGroupId.Trim()).ResourceGroup;

                var virtualMachinesExistInResourceGroup = await this.FrontdoorEngine
                                                                    .CheckIfVirtualMachineExistInResourceGroup(
                                                                      publisherTenantId,
                                                                      ProviderConstants.Databricks.ResourceProviderNamespace,
                                                                      existingEntity.SubscriptionId,
                                                                      managedResourceGroupName,
                                                                      ProviderConstants.Compute.VirtualMachinesApiVersion);

                if (virtualMachinesExistInResourceGroup)
                {
                    throw new ErrorResponseMessageException(
                        HttpStatusCode.BadRequest,
                        ErrorResponseCode.WorkspaceUpdateNotAllowed,
                        ErrorResponseMessages.WorkspaceUpdateNotAllowedForActiveCluster.ToLocalizedMessage());
                }

                var diskEncryptionResponse = await this.UpdateManagedDiskEncryption(publisherTenantId, incomingEntity, existingEntity);

                if (diskEncryptionResponse == null)
                {
                    this.Logger.LogDebug(operationName, $"Failed to create disk encryption set in managed resource group - '{existingEntity.Properties.ManagedResourceGroupId}'.");

                    throw new ErrorResponseMessageException(
                        HttpStatusCode.InternalServerError,
                        ErrorResponseCode.DiskEncryptionSetCreateFailed,
                        ErrorResponseMessages.DiskEncryptionSetCreateFailed.ToLocalizedMessage(existingEntity.GetFullyQualifiedResourceId()));
                }

                var existingEncryptionProperties = existingEntity.Properties.EncryptionProperties;

                if (existingEncryptionProperties == null)
                {
                    existingEncryptionProperties = incomingEntity.Properties.EncryptionProperties;
                }
                else if (existingEncryptionProperties.EncryptionEntities == null)
                {
                    existingEncryptionProperties.EncryptionEntities = incomingEntity.Properties.EncryptionProperties.EncryptionEntities;
                }
                else
                {
                    existingEncryptionProperties.EncryptionEntities.ManagedDisk = incomingEntity.Properties.EncryptionProperties.EncryptionEntities.ManagedDisk;
                }

                this.Logger.LogDebug(operationName, $"Successfully created disk encryption set for workspaceId - '{incomingEntity.GetFullyQualifiedResourceId()}'.");

                // if request succeeds, update properties and notify databricks
                // if request fails, throw error
                // save existing details, if notification fails, roll back to existing

                existingEntity.Properties.EncryptionProperties = existingEncryptionProperties;
                existingEntity.PopulateManagedDiskEncryptionDetails(diskEncryptionResponse);
                incomingEntity.PopulateManagedDiskEncryptionDetails(diskEncryptionResponse);
            }
            catch (DiskEncryptionSetOperationException exception)
            {
                var diskEncryptionSetName = Utilities.GenerateUniqueDESName(existingEntity.Properties.ManagedResourceGroupId, existingEntity.SubscriptionId);

                this.Logger.LogDebug(operationName, $"Failed to create disk encryption set in managed resource group - '{existingEntity.Properties.ManagedResourceGroupId}'. " +
                    $"{ErrorResponseMessages.DiskEncryptionSetCreateFailed.ToLocalizedMessage(diskEncryptionSetName, exception.Message)}");

                throw;
            }
        }

        /// <summary>
        /// Perform workspace update for Managed Disk Encryption
        /// </summary>
        /// <param name="publisherTenantId">publisher tenant Id</param>
        /// <param name="incomingEntity">input appliance</param>
        /// <param name="existingEntity">existing appliance</param>
        /// <returns>returns DiskEncryptionSetDefinition task</returns>
        private async Task<DiskEncryptionSetDefinition> UpdateManagedDiskEncryption(
            string publisherTenantId,
            ApplianceEntity incomingEntity,
            ApplianceEntity existingEntity)
        {
            var managedDiskEncryption = incomingEntity.Properties.EncryptionProperties.EncryptionEntities.ManagedDisk;
            var managedDiskKeyvaultProperties = managedDiskEncryption.KeyVaultProperties;

            var activeKey = UriTemplateEngine.GetDiskEncryptionSetActiveKeyUri(
                managedDiskKeyvaultProperties.KeyVaultUri,
                managedDiskKeyvaultProperties.KeyName,
                managedDiskKeyvaultProperties.KeyVersion);

            var diskEncryptionSetDefinition = new DiskEncryptionSetDefinition()
            {
                Location = existingEntity.Location,
                Identity = new JObject(
                    new JProperty(
                        ProviderConstants.Authorizations.TypeProperty,
                        ProviderConstants.Storage.SystemAssigned)
                    ),
                Properties = new JObject(
                    new JProperty(
                        ProviderConstants.Compute.DiskEncryptionSetActiveKeyProperty,
                        new JObject(
                            new JProperty(
                                ProviderConstants.Compute.keyUrl,
                                activeKey)
                            )),
                    new JProperty(
                        ProviderConstants.Compute.DiskEncryptionSetEncryptionTypeProperty,
                        ProviderConstants.Compute.EncryptionAtRestWithCustomerKey
                        ),
                    new JProperty(
                        ProviderConstants.Compute.RotationToLatestKeyVersionEnabled,
                        managedDiskEncryption.RotationToLatestKeyVersionEnabled
                        )),
                Tags = incomingEntity.Tags
            };

            try
            {
                var initialDiskEncryptionResponse = await this.FrontdoorEngine
                    .CreateManagedDiskEncryptionSetWithCmk(
                        managedResourceGroupId: incomingEntity.Properties.ManagedResourceGroupId,
                        authenticationTenantId: publisherTenantId,
                        resourceProviderNamespace: ProviderConstants.Databricks.ResourceProviderNamespace,
                        subscriptionId: existingEntity.SubscriptionId,
                        diskEncryptionSetCreateRequest: diskEncryptionSetDefinition)
                    .ConfigureAwait(continueOnCapturedContext: false);

                if (initialDiskEncryptionResponse == null)
                {
                    this.Logger.LogDebug(
                        operationName: "ApplicationApiEngine.UpdateManagedDiskEncryption",
                        $"The creation of disk encryption set failed.");

                    var diskEncryptionSetName = Utilities.GenerateUniqueDESName(existingEntity.Properties.ManagedResourceGroupId, existingEntity.SubscriptionId);

                    throw new ErrorResponseMessageException(
                        HttpStatusCode.InternalServerError,
                        ErrorResponseCode.DiskEncryptionSetCreateFailed,
                        ErrorResponseMessages.DiskEncryptionSetCreateFailed.ToLocalizedMessage(diskEncryptionSetName));
                }

                var diskEncryptionSetId = Utilities.GetDiskEncryptionSetId(incomingEntity.Properties.ManagedResourceGroupId, existingEntity.SubscriptionId);

                /// Get the async operation status using the azure asynchronous operation URL in the Azure-AsyncOperation header
                var diskEncryptionSetOperationStatusResponse = await this.ApplianceEngine.GetDiskEncryptionSetOperationStatus(
                    DiskEncryptionSetOperation.CreateDiskEncryptionSet,
                    diskEncryptionSetId,
                    publisherTenantId,
                    ProviderConstants.Databricks.ResourceProviderNamespace,
                    initialDiskEncryptionResponse.Headers.Location);

                this.Logger.LogDebug(
                    operationName: "ApplianceEngine.ConfigureManagedDiskEncryption",
                    $"Disk Encryption Set Operation Response - '{diskEncryptionSetOperationStatusResponse.ToJson()}'.");

                return diskEncryptionSetOperationStatusResponse.DiskEncryptionSetDefinition;
            }
            catch (DiskEncryptionSetOperationException)
            {
                this.Logger.LogError(
                    operationName: "ApplianceEngine.ConfigureManagedDiskEncryption",
                    $"Failed to create disk encryption set in managed resource group - '{incomingEntity.Properties.ManagedResourceGroupId}'.");

                throw;
            }
        }

        #endregion
    }
}